import time
import os
import random
import requests
import json
from datetime import datetime
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
try:
    from .bit_api import *
except ImportError:
    from bit_api import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from urllib.parse import urlparse
import re
import concurrent.futures
import pyperclip
import tkinter as tk
from tkinter import ttk
import paramiko
import csv
from datetime import datetime, timedelta



APP_KEY = "f08bf7f7cfe8c038"
SIGN = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="
JZTJ_WORKSHEET_ID = "jztj"
OWNER_ID = "b6c64060-3fbb-44c5-87ee-52c5f04e50af"
BJK_WORKSHEET_ID = "jzbjk"

# 企业微信通知配置
# WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
# WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9475b2e-4c9a-42a5-abbd-5072ea1fea08"

def check_login_status(driver):
    """检测登录状态 - 检查是否有login-container类"""
    try:
        # 检查是否有登录容器
        login_containers = driver.find_elements(By.CLASS_NAME, "login-container")
        if login_containers:
            for container in login_containers:
                if container.is_displayed():
                    return False  # 发现登录页面，表示未登录
        return True  # 没有发现登录页面，表示已登录
    except Exception as e:
        print(f"⚠️ 检测登录状态失败: {e}")
        return True  # 默认认为已登录，避免误判

def check_qrcode_verification(driver):
    """检测二维码验证 - 检查是否有二维码验证弹窗"""
    try:
        # 检查二维码验证相关元素
        qrcode_selectors = [
            ".static-content .qrcode-desc",
            ".qrcode-container",
            ".qrcode-img",
            "[class*='qrcode']"
        ]

        for selector in qrcode_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        # 进一步检查是否包含特定文本
                        if "小红书App" in element.text or "扫一扫" in element.text or "扫码验证" in element.text:
                            return True
                        # 或者检查是否是二维码图片
                        if "qrcode-img" in element.get_attribute("class"):
                            return True
            except:
                continue

        return False  # 没有发现二维码验证
    except Exception as e:
        print(f"⚠️ 检测二维码验证失败: {e}")
        return False  # 默认认为没有验证，避免误判



def send_to_wechat_group(webhook_url: str, message: str, userid: str = None):
    """发送企业微信群通知"""
    # 如果有 userid，前面加 @
    if userid:
        content = f"<@{userid}>\n{message}"
    else:
        content = message

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            print("✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
    except Exception as e:
        print(f"❌ 发送企业微信通知时出错：{e}")


def fetch_qw_phone(monitor_xhs_number: str) -> str:
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    payload = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "account_config",
        "filters": [
            {
                "controlId": "id",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 1,
                "value": monitor_xhs_number
            }
        ]
    }

    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        data = response.json()
        if data.get("success") and data.get("data", {}).get("rows"):
            row = data["data"]["rows"][0]
            return row.get("qw_phone", "")  # 只返回手机号
    except Exception as e:
        print(f"获取企业微信手机号失败：{e}")
    return ""

def collect_notes_statistics(driver, browser_id, browser_name, xhs_account):
    """收集笔记统计数据"""
    print(f"📊 开始收集账号 {xhs_account} 的笔记统计数据...")

    try:
        # 访问笔记管理页面
        driver.get('https://creator.xiaohongshu.com/new/note-manager')
        time.sleep(5)

        # 检查登录状态
        # if not check_login_status(driver):
        #     print(f"🔐 浏览器 {browser_id} 未登录，停止统计操作")
        #     handle_login_logout(browser_id, browser_name, xhs_account)
        #     return

        # 滚动加载所有笔记
        print("🔄 开始滚动加载所有笔记...")

        # 等待页面初始加载完成
        time.sleep(5)

        # 尝试获取总笔记数量
        total_notes_expected = 0
        try:
            # 从标签页标题中获取总数，如"全部笔记(31)"
            tab_title = driver.find_element(By.CSS_SELECTOR, '.tab-active span').text
            import re
            match = re.search(r'\((\d+)\)', tab_title)
            if match:
                total_notes_expected = int(match.group(1))
                print(f"📊 页面显示总共有 {total_notes_expected} 个笔记")
        except:
            print("📊 无法获取总笔记数量，将使用滚动检测")

        # 获取初始笔记数量
        initial_notes = driver.find_elements(By.CSS_SELECTOR, '.note')
        print(f"📋 初始加载了 {len(initial_notes)} 个笔记")

        scroll_count = 0
        max_scrolls = 100  # 增加最大滚动次数
        no_new_content_count = 0

        # 找到正确的滚动容器
        try:
            content_container = driver.find_element(By.CSS_SELECTOR, '.content')
            print("✅ 找到滚动容器 .content")
        except:
            content_container = None
            print("⚠️ 未找到 .content 容器，使用页面滚动")

        while scroll_count < max_scrolls:
            # 记录滚动前的笔记数量
            before_scroll_notes = driver.find_elements(By.CSS_SELECTOR, '.note')
            before_count = len(before_scroll_notes)

            print(f"📊 当前已加载 {before_count} 个笔记，目标: {total_notes_expected} 个")

            # 如果已经加载了预期的所有笔记，停止滚动
            if total_notes_expected > 0 and before_count >= total_notes_expected:
                print(f"✅ 已加载所有 {before_count} 个笔记")
                break

            # 使用正确的滚动容器进行滚动
            if content_container and before_scroll_notes:
                try:
                    # 策略1: 在 .content 容器中逐步滚动 500px
                    driver.execute_script("""
                        arguments[0].scrollTop = arguments[0].scrollTop + 500;
                        arguments[0].dispatchEvent(new Event('scroll'));
                    """, content_container)
                    time.sleep(1)

                    # 策略2: 让最后一个笔记元素滚入视口
                    last_note = before_scroll_notes[-1]
                    driver.execute_script("arguments[0].scrollIntoView({block: 'end'});", last_note)
                    time.sleep(2)

                    print(f"📜 在 .content 容器中滚动，当前第 {before_count} 个笔记")

                except Exception as e:
                    print(f"⚠️ 容器滚动失败: {e}")
                    # 备用方案
                    driver.execute_script("window.scrollBy(0, 500);")
                    time.sleep(2)
            else:
                # 备用方案：页面滚动
                driver.execute_script("window.scrollBy(0, 500);")
                time.sleep(2)
                print("📜 使用页面滚动")

            # 等待懒加载
            time.sleep(3)

            # 检查是否有新笔记加载
            after_scroll_notes = driver.find_elements(By.CSS_SELECTOR, '.note')
            after_count = len(after_scroll_notes)

            scroll_count += 1

            if after_count > before_count:
                print(f"📜 滚动第 {scroll_count} 次，新增 {after_count - before_count} 个笔记，总计: {after_count}")
                no_new_content_count = 0

                # 如果接近目标数量，减少等待时间
                if total_notes_expected > 0 and after_count >= total_notes_expected * 0.9:
                    print("🎯 接近目标数量，继续精确滚动...")

            else:
                no_new_content_count += 1
                print(f"📜 滚动第 {scroll_count} 次，没有新增笔记，总计: {after_count}")

                # 如果没有达到预期数量，增加尝试次数
                max_no_content = 5 if total_notes_expected > 0 and after_count < total_notes_expected else 3

                if no_new_content_count >= max_no_content:
                    print(f"✅ 连续 {max_no_content} 次滚动无新内容，停止滚动")
                    break

                # 尝试不同的滚动方式
                if no_new_content_count == 2:
                    print("🔄 尝试 JS 动画滚动（模拟手动拖动）...")
                    try:
                        if content_container:
                            driver.execute_script("""
                                let el = arguments[0];
                                let total = 0;
                                let step = 100;
                                let timer = setInterval(() => {
                                    el.scrollTop += step;
                                    el.dispatchEvent(new Event('scroll'));
                                    total += step;
                                    if (total >= 2000) clearInterval(timer);
                                }, 200);
                            """, content_container)
                            time.sleep(5)  # 等待动画完成
                            print("🔄 完成 JS 动画滚动")
                        else:
                            # 页面级动画滚动
                            driver.execute_script("""
                                let total = 0;
                                let step = 100;
                                let timer = setInterval(() => {
                                    window.scrollBy(0, step);
                                    total += step;
                                    if (total >= 2000) clearInterval(timer);
                                }, 200);
                            """)
                            time.sleep(5)
                            print("🔄 完成页面动画滚动")
                    except Exception as e:
                        print(f"⚠️ 动画滚动失败: {e}")

                elif no_new_content_count == 4:
                    print("🔄 尝试强制刷新页面内容...")
                    driver.execute_script("location.reload();")
                    time.sleep(8)  # 等待页面重新加载

        # 最终统计
        final_notes = driver.find_elements(By.CSS_SELECTOR, '.note')
        print(f"🎉 滚动完成，共加载 {len(final_notes)} 个笔记")

        # 滚动回顶部
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)

        # 提取笔记数据
        print("📝 开始提取笔记数据...")
        notes_data = []

        try:
            # 等待笔记元素加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '.note'))
            )

            # 获取所有笔记元素
            note_elements = driver.find_elements(By.CSS_SELECTOR, '.note')
            print(f"📋 找到 {len(note_elements)} 个笔记")

            for i, note_element in enumerate(note_elements):
                try:
                    # 提取笔记标题
                    title_element = note_element.find_element(By.CSS_SELECTOR, '.title')
                    title = title_element.text.strip()

                    if not title:
                        print(f"⚠️ 第 {i+1} 个笔记标题为空，跳过")
                        continue

                    # 提取数据统计 - 按HTML结构中的顺序：浏览量、评论、点赞、收藏、转发
                    icon_elements = note_element.find_elements(By.CSS_SELECTOR, '.icon_list .icon span')

                    # 默认值
                    view_count = 0
                    comment_count = 0
                    like_count = 0
                    collect_count = 0
                    share_count = 0

                    # 解析数字的函数
                    def parse_count(text):
                        if not text:
                            return 0
                        text = text.strip()
                        if text.isdigit():
                            return int(text)
                        elif 'k' in text.lower():
                            try:
                                return int(float(text.lower().replace('k', '')) * 1000)
                            except:
                                return 0
                        elif '万' in text:
                            try:
                                return int(float(text.replace('万', '')) * 10000)
                            except:
                                return 0
                        elif 'w' in text.lower():
                            try:
                                return int(float(text.lower().replace('w', '')) * 10000)
                            except:
                                return 0
                        else:
                            # 提取数字部分
                            import re
                            numbers = re.findall(r'\d+', text)
                            return int(numbers[0]) if numbers else 0

                    # 按顺序提取数据
                    if len(icon_elements) >= 5:
                        view_count = parse_count(icon_elements[0].text)
                        comment_count = parse_count(icon_elements[1].text)
                        like_count = parse_count(icon_elements[2].text)
                        collect_count = parse_count(icon_elements[3].text)
                        share_count = parse_count(icon_elements[4].text)
                    elif len(icon_elements) > 0:
                        # 如果数据不足5个，尽量提取可用的
                        for idx, element in enumerate(icon_elements):
                            count = parse_count(element.text)
                            if idx == 0:
                                view_count = count
                            elif idx == 1:
                                comment_count = count
                            elif idx == 2:
                                like_count = count
                            elif idx == 3:
                                collect_count = count
                            elif idx == 4:
                                share_count = count

                    note_data = {
                        "title": title,
                        "view_count": view_count,
                        "comment_count": comment_count,
                        "like_count": like_count,
                        "collect_count": collect_count,
                        "share_count": share_count,
                        "account_name": browser_name,
                        "xhs_account": xhs_account,
                        "extract_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    notes_data.append(note_data)
                    print(f"📊 提取笔记 {i+1}: {title[:30]}... - 浏览:{view_count} 评论:{comment_count} 点赞:{like_count}")

                except Exception as e:
                    print(f"❌ 提取第 {i+1} 个笔记数据失败: {e}")
                    continue

        except Exception as e:
            print(f"❌ 提取笔记数据失败: {e}")
            return

        # 同步到明道云
        if notes_data:
            sync_notes_to_mingdao(notes_data)
            print(f"✅ 账号 {xhs_account} 统计完成，共处理 {len(notes_data)} 条笔记")
        else:
            print(f"⚠️ 账号 {xhs_account} 没有提取到笔记数据")

    except Exception as e:
        print(f"❌ 收集账号 {xhs_account} 统计数据失败: {e}")

def check_existing_notes_in_mingdao(xhs_account):
    """检查明道云中已存在的笔记（按账号过滤）"""
    print(f"🔍 检查明道云中账号 {xhs_account} 已存在的笔记...")

    existing_notes = {}

    try:
        # 查询明道云中的现有数据（按小红书账号过滤）
        query_payload = {
            "appKey": "f08bf7f7cfe8c038",
            "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
            "worksheetId": "jzbjk",
            "pageSize": 1000,
            "pageIndex": 1,
            "filters": [
                {
                    "controlId": "xhsh",
                    "filterType": 1,
                    "value": xhs_account
                }
            ]
        }

        response = requests.post(
            "https://api.mingdao.com/v2/open/worksheet/getFilterRows",
            json=query_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("success") and result.get("data", {}).get("rows"):
                for row in result["data"]["rows"]:
                    title = row.get("bjbt", "")
                    account = row.get("xhsh", "")
                    row_id = row.get("rowid", "")

                    # 使用标题+账号作为唯一标识
                    key = f"{title}_{account}"
                    existing_notes[key] = row_id

                print(f"📋 明道云中账号 {xhs_account} 已存在 {len(existing_notes)} 条笔记记录")
            else:
                print(f"📋 明道云中账号 {xhs_account} 暂无笔记记录")
        else:
            print(f"❌ 查询明道云数据失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"❌ 查询明道云数据失败: {e}")

    return existing_notes

def sync_notes_to_mingdao(notes_data):
    """将笔记数据同步到明道云（支持新增和更新）"""
    print(f"🔄 开始同步 {len(notes_data)} 条数据到明道云...")

    if not notes_data:
        print("⚠️ 没有数据需要同步")
        return

    # 检查已存在的笔记（从第一条数据中获取账号信息）
    xhs_account = notes_data[0]['xhs_account'] if notes_data else ""
    existing_notes = check_existing_notes_in_mingdao(xhs_account)

    new_notes = []  # 需要新增的笔记
    update_notes = []  # 需要更新的笔记

    for note in notes_data:
        key = f"{note['title']}_{note['xhs_account']}"

        note_data = [
            {"controlId": "bjbt", "value": note["title"]},
            {"controlId": "nicheng", "value": note["account_name"]},
            {"controlId": "xhsh", "value": note["xhs_account"]},
            {"controlId": "xiaoyanjing_number", "value": str(note["view_count"])},
            {"controlId": "pinglun_number", "value": str(note["comment_count"])},
            {"controlId": "dianzan_number", "value": str(note["like_count"])},
            {"controlId": "shoucang_number", "value": str(note["collect_count"])},
            {"controlId": "zhuanfa_number", "value": str(note["share_count"])},
            {"controlId": "ownerid", "value": OWNER_ID}
        ]

        if key in existing_notes:
            # 需要更新
            update_notes.append({
                "rowId": existing_notes[key],
                "controls": note_data
            })
        else:
            # 需要新增
            new_notes.append(note_data)

    # 新增笔记
    if new_notes:
        print(f"📝 新增 {len(new_notes)} 条笔记...")
        add_payload = {
            "appKey": APP_KEY,
            "sign": SIGN,
            "worksheetId": BJK_WORKSHEET_ID,
            "triggerWorkflow": True,
            "returnRowIds": "true",
            "rows": new_notes
        }

        try:
            response = requests.post(
                "https://api.mingdao.com/v2/open/worksheet/addRows",
                json=add_payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print(f"✅ 成功新增 {len(new_notes)} 条笔记")
                else:
                    print(f"❌ 新增笔记失败: {result}")
            else:
                print(f"❌ 新增笔记请求失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"❌ 新增笔记失败: {e}")

    # 更新笔记
    if update_notes:
        print(f"🔄 更新 {len(update_notes)} 条笔记...")

        # 批量更新
        for update_note in update_notes:
            update_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": BJK_WORKSHEET_ID,
                "triggerWorkflow": True,
                "controls": update_note["controls"],
                "rowId": update_note["rowId"]
            }

            try:
                response = requests.post(
                    "https://api.mingdao.com/v2/open/worksheet/editRow",
                    json=update_payload,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        print(f"✅ 成功更新笔记: {update_note['controls'][0]['value'][:30]}...")
                    else:
                        print(f"❌ 更新笔记失败: {result}")
                else:
                    print(f"❌ 更新笔记请求失败，状态码: {response.status_code}")

            except Exception as e:
                print(f"❌ 更新笔记失败: {e}")

    print(f"🎉 数据同步完成！新增: {len(new_notes)} 条，更新: {len(update_notes)} 条")

def handle_account_exception(browser_id, browser_name, xhs_account, exception_type="掉登录", auto_action="已自动关闭浏览器"):
    """处理账号异常情况"""
    print(f"⚠️ 检测到浏览器 {browser_id} ({browser_name}) {exception_type}")

    # 关闭浏览器
    try:
        closeBrowser(browser_id)
        print(f"✅ 浏览器 {browser_id} 已关闭")
    except Exception as e:
        print(f"⚠️ 关闭浏览器失败: {e}")

    # 根据异常类型设置不同的图标和标题
    exception_icons = {
        "掉登录": "🔐",
        "账号异常": "⚠️",
        "验证码": "🚨",
        "二维码验证": "📱",
        "封号": "🚫",
        "限流": "⏳"
    }

    icon = exception_icons.get(exception_type, "⚠️")

    # 发送企业微信通知
    # message = (
    #     f"### {icon} 小红书账号异常提醒\n"
    #     f"- **异常类型**: {exception_type}\n"
    #     f"- **窗口ID**: {browser_id}\n"
    #     f"- **窗口名称**: {browser_name}\n"
    #     f"- **小红书账号**: {xhs_account}\n"
    #     f"- **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    #     # f"- **处理状态**: {auto_action}\n\n"
    #     f"占用线程资源，请及时处理！"
    # )

    # userid = fetch_qw_phone(xhs_account)
    # send_to_wechat_group(WECHAT_WEBHOOK_URL, message, userid)
    return False  # 返回False表示需要停止当前操作

def handle_login_logout(browser_id, browser_name, xhs_account):
    """处理掉登录情况 - 兼容性函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "掉登录", "已自动关闭浏览器")

def handle_captcha_detected(browser_id, browser_name, xhs_account):
    """处理验证码检测情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "验证码", "已暂停操作，等待手动处理")

def handle_account_banned(browser_id, browser_name, xhs_account):
    """处理账号封禁情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "封号", "已自动关闭浏览器")

def handle_account_limited(browser_id, browser_name, xhs_account):
    """处理账号限流情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "限流", "已自动关闭浏览器")

def handle_general_exception(browser_id, browser_name, xhs_account):
    """处理一般账号异常情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "账号异常", "已自动关闭浏览器")

def handle_qrcode_verification(browser_id, browser_name, xhs_account):
    """处理二维码验证情况"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "二维码验证", "已自动关闭浏览器")

# ============================================================================
# 账号异常处理使用说明：
#
# 1. 掉登录：    handle_login_logout(browser_id, browser_name, xhs_account)
# 2. 验证码：    handle_captcha_detected(browser_id, browser_name, xhs_account)
# 3. 二维码验证：handle_qrcode_verification(browser_id, browser_name, xhs_account)
# 4. 封号：      handle_account_banned(browser_id, browser_name, xhs_account)
# 5. 限流：      handle_account_limited(browser_id, browser_name, xhs_account)
# 6. 其他：      handle_general_exception(browser_id, browser_name, xhs_account)
#
# 或者直接使用通用函数：
# handle_account_exception(browser_id, browser_name, xhs_account, "自定义异常类型", "自定义处理状态")
#
# 二维码验证检测：
# - 主要在养号流程中点击笔记后检测
# - 检测元素：.qrcode-desc, .qrcode-container, .qrcode-img 等
# - 检测文本：包含"小红书App"、"扫一扫"、"扫码验证"等关键词
# ============================================================================

def parse_count(text):
    try:
        if not text:
            return 0
        text = text.replace(",", "").strip().lower()  # 去千位符、空格、小写化
        if 'w' in text:
            return int(float(text.replace('w', '')) * 10000)
        if '万' in text:
            return int(float(text.replace('万', '')) * 10000)
        return int(float(text))  # 防止 '12.0' 类型报错
    except:
        return 0

    
def account_matrix(driver):
    time.sleep(5)

    try:

        try:
            username_element = WebDriverWait(driver, 180).until(EC.presence_of_element_located((By.CSS_SELECTOR, ".account-name")))
            username = username_element.text.strip()
        except Exception as e:
            username = None
            print("未找到昵称", e)

        # 提取小红书号
        try:
            xhs_id_element = WebDriverWait(driver, 180).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(), '小红书账号')]")
                )
            )
            xhs_number = xhs_id_element.text.split(":")[-1].strip()  # 英文冒号
        except Exception as e:
            xhs_number = None
            print("未找到小红书号", e)

        # 如果两个都没获取到，直接返回，不执行后面逻辑
        if not username or not xhs_number:
            print("❌ 昵称或小红书号缺失，跳过该账号")
            return    

        # 提取关注、粉丝、获赞与收藏
        # 等待主容器加载
        stats_container = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.description-text")))

        # 抓取三个数值
        elements = stats_container.find_elements(By.CSS_SELECTOR, "span.numerical")
        if len(elements) >= 3:
            following_count = elements[0].text
            follower_count = elements[1].text
            like_fav_count = elements[2].text
        else:
            print("未能成功提取到所有数据")

        # 打印信息
        # print(f"小红书号：{xhs_number}")
        # print(f"昵称：{username}")
        # print(f"关注：{following_count}")
        # print(f"粉丝：{follower_count}")
        # print(f"获赞与收藏：{like_fav_count}")

        # 查询是否已有记录
        search_url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
        filter_payload = {
            "appKey": APP_KEY,
            "sign": SIGN,
            "worksheetId": JZTJ_WORKSHEET_ID,
            "pageSize": 1,
            "pageIndex": 1,
            "filters": [
                {
                    "controlId": "xhsh",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 3,
                    "value": xhs_number
                }
            ]
        }
        res = requests.post(search_url, json=filter_payload)
        result = res.json()

        if result.get("data", {}).get("rows"):
            # 已存在，走修改接口
            row_id = result["data"]["rows"][0]["rowid"]
            edit_url = "https://api.mingdao.com/v2/open/worksheet/editRow"
            edit_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": JZTJ_WORKSHEET_ID,
                "rowId": row_id,
                "triggerWorkflow": True,
                "controls": [
                    {"controlId": "zhmc", "value": username},
                    {"controlId": "xhsh", "value": xhs_number},
                    {"controlId": "guanzhu", "value": following_count},
                    {"controlId": "fensi", "value": follower_count},
                    {"controlId": "hzysc", "value": like_fav_count},
                    {"controlId": "guanzhu_count", "value": parse_count(following_count)},
                    {"controlId": "fensi_count", "value": parse_count(follower_count)},
                    {"controlId": "hzysc_count", "value": parse_count(like_fav_count)}
                ]
            }
            res = requests.post(edit_url, json=edit_payload)
            print("✅ 修改成功矩阵管理" if res.status_code == 200 else f"❌ 修改失败: {res.text}")
        else:
            # 不存在，走新增接口
            add_url = "https://api.mingdao.com/v2/open/worksheet/addRow"
            add_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": JZTJ_WORKSHEET_ID,
                "triggerWorkflow": True,
                "controls": [
                    {"controlId": "zhmc", "value": username},
                    {"controlId": "xhsh", "value": xhs_number},
                    {"controlId": "guanzhu", "value": following_count},
                    {"controlId": "fensi", "value": follower_count},
                    {"controlId": "hzysc", "value": like_fav_count},
                    {"controlId": "guanzhu_count", "value": parse_count(following_count)},
                    {"controlId": "fensi_count", "value": parse_count(follower_count)},
                    {"controlId": "hzysc_count", "value": parse_count(like_fav_count)}
                ]
            }
            res = requests.post(add_url, json=add_payload)
            print("✅ 新增成功矩阵管理" if res.status_code == 200 else f"❌ 新增失败: {res.text}")

        return xhs_number, username

    except Exception as e:
        print(f"获取账号信息失败：{e}")
        return None, None


# 抓取笔记信息
def scrape_notes(driver, xhs_number, nickname):
    print("开始抓取笔记信息")
    try:
        WebDriverWait(driver, 180).until(
            EC.presence_of_all_elements_located((By.CLASS_NAME, "note"))
        )
    except Exception as e:
        print("❌ 加载 note 元素超时：", e)
        return

    notes = driver.find_elements(By.CLASS_NAME, "note")

    for note in notes:
        try:
            title = note.find_element(By.CLASS_NAME, "title").text.strip()
            time = note.find_element(By.CLASS_NAME, "time").text.strip()

            icons = note.find_elements(By.CLASS_NAME, "icon")
            icon_values = [icon.text.strip() for icon in icons]

            view_count = icon_values[0] if len(icon_values) > 0 else "0"
            comment_count = icon_values[1] if len(icon_values) > 1 else "0"
            like_count = icon_values[2] if len(icon_values) > 2 else "0"
            favorite_count = icon_values[3] if len(icon_values) > 3 else "0"
            share_count = icon_values[4] if len(icon_values) > 4 else "0"


            # 提取封面图链接
            cover_img_elem = note.find_element(By.CSS_SELECTOR, ".media-bg")
            cover_style = cover_img_elem.get_attribute("style")
            match = re.search(r'url\("([^"]+)"\)', cover_style)
            cover_img_url = match.group(1) if match else ""

            # ✅ 先查询是否已存在这条笔记（根据 xhs_number + title）
            check_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": BJK_WORKSHEET_ID,
                "pageSize": 100,
                "pageIndex": 1,
                "filters": [
                    {"controlId": "xhsh", "dataType": 2, "spliceType": 1, "filterType": 2,"value": xhs_number},
                    {"controlId": "bjbt", "dataType": 2, "spliceType": 1, "filterType": 2,"value": title}
                ]
            }

            check_res = requests.post("https://api.mingdao.com/v2/open/worksheet/getFilterRows", json=check_payload)
            row_id = None
            if check_res.status_code == 200:
                rows = check_res.json().get("data", {}).get("rows", [])
                if rows:
                    row_id = rows[0]["rowid"]

            # 构建字段
            controls = [
                {"controlId": "bjbt", "value": title},
                {"controlId": "xiaoyanjing", "value": view_count},
                {"controlId": "pinglun", "value": comment_count},
                {"controlId": "dianzan", "value": like_count},
                {"controlId": "shoucang", "value": favorite_count},
                {"controlId": "zhuanfa", "value": share_count},
                {"controlId": "xiaoyanjing_number", "value": parse_count(view_count)},
                {"controlId": "pinglun_number", "value": parse_count(comment_count)},
                {"controlId": "dianzan_number", "value": parse_count(like_count)},
                {"controlId": "shoucang_number", "value": parse_count(favorite_count)},
                {"controlId": "zhuanfa_number", "value": parse_count(share_count)},
                {"controlId": "xhsh", "value": xhs_number},
                {"controlId": "nicheng", "value": nickname},
                {"controlId": "fengmian", "value": cover_img_url}
            ]
            if row_id:
                # ✅ 更新笔记
                payload = {
                    "appKey": APP_KEY,
                    "sign": SIGN,
                    "worksheetId": BJK_WORKSHEET_ID,
                    "rowId": row_id,
                    "triggerWorkflow": True,
                    "controls": controls
                }
                res = requests.post("https://api.mingdao.com/v2/open/worksheet/editRow", json=payload)
                print(f"📝 已更新：{title}" if res.status_code == 200 else f"❌ 更新失败：{title}，响应：{res.text}")
            else:
                # ✅ 新增笔记
                payload = {
                    "appKey": APP_KEY,
                    "sign": SIGN,
                    "worksheetId": BJK_WORKSHEET_ID,
                    "triggerWorkflow": True,
                    "controls": controls
                }
                res = requests.post("https://api.mingdao.com/v2/open/worksheet/addRow", json=payload)
                print(f"✅ 已新增：{title}" if res.status_code == 200 else f"❌ 新增失败：{title}，响应：{res.text}")

        except Exception as e:
            print("⚠️ 解析某条笔记失败：", e)


def open_browser(window_id):
    """调用 openBrowser 方法获取 Selenium 连接信息"""
    return openBrowser(window_id)  # 直接调用已有的 openBrowser 方法

def set_browser_download_path(driver, download_path=None):
    """为现有的driver设置下载路径"""
    try:
        if not download_path:
            download_path = os.path.join(os.path.expanduser("~"), "Desktop", "xhs_downloads")

        os.makedirs(download_path, exist_ok=True)  # 确保文件夹存在

        # 通过CDP设置下载路径
        driver.execute_cdp_cmd('Page.setDownloadBehavior', {
            'behavior': 'allow',
            'downloadPath': download_path
        })

        print(f"📁 已设置下载路径: {download_path}")
        return download_path

    except Exception as e:
        print(f"⚠️ 设置下载路径失败: {e}")
        # 返回默认路径
        return os.path.join(os.path.expanduser("~"), "Desktop", "xhs_downloads")

def create_chrome_driver_with_download_path(browser_id, download_path=None):
    """创建Chrome driver并设置下载路径"""
    try:
        # 获取浏览器连接信息
        res = open_browser(browser_id)
        if not res or 'data' not in res:
            print(f"❌ 无法打开浏览器 {browser_id}")
            return None, None

        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        # 创建Chrome选项（不设置prefs，因为连接到现有浏览器时会报错）
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)

        # 创建driver
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        # 连接成功后，通过CDP设置下载路径
        if not download_path:
            download_path = os.path.join(os.path.expanduser("~"), "Desktop", "xhs_downloads")

        download_path = set_browser_download_path(driver, download_path)

        return driver, download_path

    except Exception as e:
        print(f"❌ 创建Chrome driver失败: {e}")
        return None, None

def get_account_number(driver):
    """获取当前登录的小红书账号"""
    driver.get('https://www.xiaohongshu.com/explore')
    my_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, '//span[text()="我"]'))
    )
    my_button.click()
    
    # red_id_element = driver.find_element(By.XPATH, '//span[@class="user-redId"]')
    red_id_element = WebDriverWait(driver, 10).until(
        EC.visibility_of_element_located((By.XPATH, '//span[@class="user-redId"]'))
    )
    red_id = red_id_element.text.split("：")[1]
    print(f"当前小红书账号: {red_id}")
    return red_id

def check_fabu_config(browser):
    """检查是否有符合条件的发布内容"""
    full_remark = browser.get("remark", "")
    account_number = full_remark.split("_")[0].strip()
    # print(f"xxxxxxxxxxx:{account_number}")
    name = browser.get("name", "")
    print(f"检查 {name} 的待发布内容...")
    time.sleep(random.randint(5, 10))
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    headers = {"Content-Type": "application/json"}
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "pageSize": 1000,
        "pageIndex": 1,
        "controls": [],
        "filters": [
            {"controlId": "release_status", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "未发布"},
            {"controlId": "channel_type", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "小红书"}
        ]
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            res = response.json()
            if res.get("success") and "rows" in res.get("data", {}):
                rows = res["data"]["rows"]
                for row in rows:
                    account_data_str = row.get("account", "")
                    release_time = row.get("release_time")
                    account_data = json.loads(account_data_str)
                    if isinstance(account_data, list):
                        for account_item in account_data:
                            source_value = json.loads(account_item.get("sourcevalue", "{}"))
                            account_in_data = source_value.get("66d7fffe98435d4ec600ca08", "")
                            if account_in_data.strip().lower() == account_number.strip().lower():
                                release_time_obj = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
                                release_type = row.get("release_type", "")

                                # 如果是定时发布，直接返回记录（不检查时间）
                                if release_type == "定时发布":
                                    print(f"📅 发现定时发布任务，发布时间: {release_time}")
                                    return row

                                # 如果是立即发布，检查时间是否到达
                                if datetime.now() >= release_time_obj:
                                    print(f"⏰ 发现立即发布任务，发布时间已到: {release_time}")
                                    return row  # 直接返回符合条件的发布记录
    except Exception as e:
        print(f"请求出错: {e}")
    return None

def clean_filename(filename):
    """
    清理文件名中的非法字符（包括 ?、=、&、: 等），确保在 Windows 操作系统中合法。
    :param filename: 原始文件名
    :return: 清理后的合法文件名
    """
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def get_filename_from_url(url):
    """
    从 URL 中提取文件名，并去掉查询参数部分。
    :param url: 文件的下载 URL
    :return: 清理后的文件名
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)  # 提取 URL 中的文件名部分
    return file_name.split('?')[0]  # 去掉查询参数部分

def download_media(tp_sp):
    """下载图片或视频并返回本地路径列表"""
    if isinstance(tp_sp, str):
        tp_sp = json.loads(tp_sp)

    if not isinstance(tp_sp, list) or not tp_sp:
        print("未找到有效的 tp_sp 数据")
        raise ValueError("未找到有效的 tp_sp 数据")
    
    media_paths = []
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    fastdown_folder = os.path.join(desktop_path, "fastdown")
    os.makedirs(fastdown_folder, exist_ok=True)
    
    for media_info in tp_sp:
        download_url = media_info.get("DownloadUrl")
        if not download_url:
            print("未找到有效的文件下载 URL")
            raise ValueError("未找到有效的文件下载 URL")
        
        response = requests.get(download_url, stream=True, timeout=30)
        if response.status_code != 200:
            raise Exception(f"文件下载失败，状态码: {response.status_code}")
        
        file_name = clean_filename(get_filename_from_url(download_url))
        local_file_path = os.path.join(fastdown_folder, file_name)
        
        with open(local_file_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
        
        print(f"文件已保存到本地路径: {local_file_path}")
        media_paths.append(local_file_path)
    
    return media_paths

def get_media_type(file_name):
    """
    根据文件扩展名判断文件类型（图片或视频）。
    :param file_name: 文件名
    :return: 媒体类型 ('image' 或 'video')
    """
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    _, ext = os.path.splitext(file_name.lower())
    
    if ext in image_extensions:
        return 'image'
    elif ext in video_extensions:
        return 'video'
    else:
        return 'unknown'  # 如果是未知类型
    

def run_automation(driver, row, browser_id, xhs_account=None, browser_name=None):
    """执行小红书自动化发布（支持图文 & 视频 + 视频封面）"""
    
    title = row.get("title", "默认标题")
    zhengwen = row.get("zhengwen", "默认正文")
    tp_sp = row.get("tp_sp", [])  # 可能包含图片或视频
    video_cover = row.get("video_cover", [])  # 视频封面（可选）

    if isinstance(video_cover, str):
        try:
            video_cover = json.loads(video_cover)  # 解析 JSON 字符串
        except json.JSONDecodeError:
            video_cover = []  # 解析失败就置为空列表
    try:
        media_paths = download_media(tp_sp)  # 下载所有图片 & 视频
        if video_cover:
            print("进入封面下载")
            cover_path = download_media(video_cover)
    except Exception as e:
        print(f"媒体下载失败: {e}")
        return

    print("执行自动化发布...")
    print(row)
    driver.get('https://creator.xiaohongshu.com/publish/publish?source=official')

    # 访问小红书页面后立即检测登录状态
    time.sleep(3)  # 等待页面加载
    # if not check_login_status(driver):
    #     print(f"🔐 访问发布页面后检测到浏览器 {browser_id} 未登录，停止发布操作")
    #     name = browser_name or f"浏览器_{browser_id}"
    #     account_name = xhs_account or "未知账号"
    #     handle_login_logout(browser_id, name, account_name)  # 暂时注释掉掉登检测
    #     return

    if not media_paths:
        print("未找到媒体文件，取消发布")
        return

    # **判断是否有视频**
    has_video = any(get_media_type(path) == "video" for path in media_paths)

    if has_video:
        # **处理视频发布**
        # 定位到 "上传视频" 标签
        upload_video_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//*[text()='上传视频']"))
        )
        upload_video_tab.click()
        time.sleep(2) 

        upload_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "creator-tab")]/span[text()="上传视频"]'))
        )
        upload_btn.click()

        # 只上传第一个视频
        video_path = next(path for path in media_paths if get_media_type(path) == "video")
        upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
        upload_input.send_keys(video_path)
        time.sleep(8)

        # 等待“视频解析中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "视频解析中")]'))
        )
        print("视频解析完成")

        # 等待“上传中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "上传中")]'))
        )
        print("视频上传完成")

        """处理封面上传"""
        try:
            # 上传封面文件
            if cover_path:
                print(cover_path)
                # 点击“设置封面”按钮
                set_cover_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "uploadCover")]'))
                )
                set_cover_btn.click()
                time.sleep(2)
                # 等待 file input 出现
                upload_input = WebDriverWait(driver, 300).until(
                    EC.presence_of_element_located((
                        By.XPATH, '//input[@type="file" and @accept="image/png, image/jpeg, image/*"]'
                    ))
                )

                # 直接传入文件路径上传
                path_to_upload = cover_path[0].replace("\\", "\\\\")
                upload_input.send_keys(path_to_upload)

                time.sleep(3)
                # 等待弹窗出现
                modal = WebDriverWait(driver, 120).until(
                    EC.visibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )

                # 在弹窗里等待“确定”按钮可点击
                confirm_button = WebDriverWait(modal, 120).until(
                    EC.element_to_be_clickable((By.XPATH, ".//button[contains(@class, 'mojito-button') and contains(., '确定')]"))
                )

                confirm_button.click()

                # 监控 `loading` 元素的 `style`，等待它变成 `display: none;`，说明封面上传成功
                WebDriverWait(driver, 120).until(
                    lambda d: 'display: none' in d.find_element(By.XPATH, '//div[contains(@class, "loading")]').get_attribute("style")
                )

                # 等待弹窗彻底关闭
                WebDriverWait(driver, 120).until(
                    EC.invisibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )
                print("弹窗已关闭！")
                print("封面上传完成！")
            else:
                print("封面路径为空，跳过上传")

        except Exception as e:
            print(f"封面选择失败: {e}")

    else:
        # **处理图文发布**
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '.creator-tab'))
        )
        tabs = driver.find_elements(By.CSS_SELECTOR, '.creator-tab')

        for i, tab in enumerate(tabs):
            try:
                title_el = tab.find_element(By.CSS_SELECTOR, 'span.title')
                title_btn = title_el.text.strip()

                if title_btn == "上传图文" and tab.is_displayed():
                    tab.click()
                    break
            except Exception as e:
                print(f"点击第 {i+1} 个上传图文时异常：{e}")

        # **处理图文封面优先上传**
        if video_cover and cover_path:
            print("检测到图文封面，优先上传封面图片")
            # 先上传封面图片
            upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
            upload_input.send_keys(cover_path[0])
            time.sleep(8)

            # 等待封面上传完成
            WebDriverWait(driver, 600).until(
                EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
            )
            print("封面图片上传完成")

            # 再上传其他图片（如果有的话）
            if media_paths:
                print("继续上传其他图片")
                # 重新定位上传元素，因为DOM已经变化
                try:
                    # 直接找到图片编辑区域的多选上传input（隐藏元素）
                    upload_input_multiple = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//input[@type="file" and @multiple and @accept=".jpg,.jpeg,.png,.webp"]'))
                    )
                    upload_input_multiple.send_keys("\n".join(media_paths))
                    print("使用多选上传input直接上传其他图片")
                except Exception as e:
                    print(f"无法上传其他图片: {e}")
                    return

                time.sleep(8)

                # 等待所有图片上传完成
                WebDriverWait(driver, 600).until(
                    EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
                )
                print("所有其他图片上传完成")
        else:
            # 没有封面，直接上传所有图片
            upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
            upload_input.send_keys("\n".join(media_paths))  # 上传多张图片
            time.sleep(8)

            # 等待所有进度条消失
            WebDriverWait(driver, 600).until(
                EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
            )

        print("所有图片上传完成，继续执行后续流程")
        time.sleep(2)

    # **填写标题、正文**
    time.sleep(2)
    # 复制标题到剪贴板
    pyperclip.copy(title)
    title_input = WebDriverWait(driver, 200).until(EC.presence_of_element_located((By.XPATH, '//input[@class="d-text" and @type="text"]')))
    driver.execute_script("arguments[0].scrollIntoView();", title_input)
    # title_input.send_keys(title)
    title_input.click()  # 确保输入框获得焦点
    title_input.send_keys(Keys.CONTROL, 'v')

    # 输入正文
    # 复制文本到剪贴板
    time.sleep(2)
    pyperclip.copy(zhengwen)
    # editor_input = driver.find_element(By.XPATH, '//div[@class="ql-editor ql-blank"]')
    editor_input = driver.find_element(By.XPATH, '//div[@class="tiptap ProseMirror"]')
    # editor_input.send_keys(zhengwen)
    # 定位输入框并粘贴
    editor_input.click()  # 确保输入框获得焦点
    editor_input.send_keys(Keys.CONTROL, 'v')  # Windows / Linux
    time.sleep(3)

    editor_input.send_keys(Keys.ENTER)
    time.sleep(2)

    # **处理话题**
    topics = row.get("topic_word", "")
    topic_list = topics.split(",") if topics else []

    for topic in topic_list:
        if topic.strip():
            driver.execute_script("arguments[0].scrollIntoView();", editor_input)
            editor_input.send_keys(f" #{topic.strip()}")
            # 等待下拉列表显示
            try:
                WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "#quill-mention-list"))
                )
                time.sleep(3)
                # 如果下拉列表出现，按下 ENTER 键
                editor_input.send_keys(Keys.ENTER)
                time.sleep(1)
            except:
                # 如果没有找到下拉列表，跳过按键
                print(f"没有找到下拉列表，跳过 {topic.strip()}")
                continue
            # time.sleep(3)
            # editor_input.send_keys(Keys.ENTER)
            # time.sleep(1)

    # **处理发布时间设置**
    release_type = row.get("release_type", "")

    if release_type == "定时发布":
        print("🕒 检测到定时发布，开始设置发布时间...")

        try:
            # 等待发布时间选择区域出现
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, '//div[contains(@class, "flexbox")]//div[contains(@class, "_title") and text()="发布时间"]'))
            )

            # 点击定时发布单选按钮
            scheduled_radio = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//label[contains(@class, "el-radio")]//span[contains(@class, "el-radio__label") and text()="定时发布"]'))
            )
            scheduled_radio.click()
            time.sleep(2)
            print("✅ 已选择定时发布")

            # 获取发布时间
            release_time_str = row.get("release_time", "")
            if not release_time_str:
                print("⚠️ 未找到 release_time 字段，使用当前时间")
                target_time = datetime.now()
            else:
                try:
                    # 解析发布时间字符串
                    target_time = datetime.strptime(release_time_str, "%Y-%m-%d %H:%M:%S")
                    print(f"📅 解析到发布时间: {release_time_str}")
                except ValueError:
                    print(f"⚠️ 发布时间格式错误: {release_time_str}，使用当前时间")
                    target_time = datetime.now()

            # 查找时间输入框并点击打开选择器
            time_input = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//input[contains(@class, "el-input__inner") and @placeholder="选择日期和时间"]'))
            )
            time_input.click()
            time.sleep(2)
            print("✅ 已打开时间选择器")

            # 等待时间选择器面板出现
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '.el-picker-panel.el-date-picker'))
            )

            # 手动设置指定的日期和时间
            try:
                # 设置日期输入框
                date_input = driver.find_element(By.XPATH, '//input[@placeholder="选择日期"]')
                date_input.clear()
                date_input.send_keys(target_time.strftime("%Y-%m-%d"))
                time.sleep(0.5)
                print(f"✅ 已设置日期: {target_time.strftime('%Y-%m-%d')}")

                # 设置时间输入框
                time_input_field = driver.find_element(By.XPATH, '//input[@placeholder="选择时间"]')
                time_input_field.clear()
                time_input_field.send_keys(target_time.strftime("%H:%M:%S"))
                time.sleep(0.5)
                print(f"✅ 已设置时间: {target_time.strftime('%H:%M:%S')}")

            except Exception as manual_error:
                print(f"⚠️ 手动设置时间失败: {manual_error}")
                # 如果手动设置失败，尝试点击"此刻"按钮作为备用方案
                try:
                    now_button = driver.find_element(By.XPATH, '//button[contains(@class, "el-picker-panel__link-btn")]//span[text()="此刻"]')
                    now_button.click()
                    time.sleep(1)
                    print("✅ 备用方案：已点击'此刻'按钮")
                except:
                    print("⚠️ 备用方案也失败，继续执行")

            # 点击确定按钮
            try:
                confirm_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, '//button[contains(@class, "el-picker-panel__link-btn")]//span[text()="确定"]'))
                )
                confirm_button.click()
                time.sleep(1)
                print("✅ 已确认时间设置")
            except:
                # 如果没有确定按钮，点击其他地方关闭选择器
                driver.execute_script("document.body.click();")
                time.sleep(1)
                print("✅ 已关闭时间选择器")

            print(f"✅ 已设置定时发布时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"⚠️ 设置定时发布时间失败: {e}")
            print("🔄 回退到立即发布模式")
            # 如果设置定时发布失败，回退到立即发布
            try:
                immediate_radio = driver.find_element(By.XPATH, '//label[contains(@class, "el-radio")]//span[contains(@class, "el-radio__label") and text()="立即发布"]')
                immediate_radio.click()
                time.sleep(1)
                print("✅ 已回退到立即发布")
            except:
                print("⚠️ 回退到立即发布也失败，继续执行发布")
    else:
        print("📤 使用立即发布模式")

    # **点击发布按钮**
    release_type = row.get("release_type", "")

    if release_type == "定时发布":
        # 定时发布时，按钮文本是"定时发布"
        try:
            publish_btn = driver.find_element(By.XPATH, '//div[@class="d-button-content"]//span[text()="定时发布"]')
            publish_btn.click()
            time.sleep(1)
            print("定时发布完成")
        except:
            # 如果找不到"定时发布"按钮，尝试找"发布"按钮作为备用
            print("⚠️ 未找到'定时发布'按钮，尝试点击'发布'按钮")
            publish_btn = driver.find_element(By.XPATH, '//div[@class="d-button-content"]//span[text()="发布"]')
            publish_btn.click()
            time.sleep(1)
            print("发布完成")
    else:
        # 立即发布时，按钮文本是"发布"
        publish_btn = driver.find_element(By.XPATH, '//div[@class="d-button-content"]//span[text()="发布"]')
        publish_btn.click()
        time.sleep(1)
        print("发布完成")

    # **更新明道状态**
    try:
        # 等待元素 "发布成功"出现，最多等待 6 秒
        WebDriverWait(driver, 6).until(
            EC.text_to_be_present_in_element((By.CSS_SELECTOR, "p.title"), "发布成功")
        )
        # **更新明道状态**
        update_mingdao_status(row)
        print("明道状态更新成功")
    except Exception as e:
        print("未检测到 '发布成功'，不更新状态和窗口")
    

def update_mingdao_status(item, status="已发布", timeout=6):
    """
    修改明道表字段状态为指定值（默认 "已发布"），针对传入的单个循环项，不做重试和异常处理。
    :param item: 包含需要更新的行记录数据（单个 JSON 对象）。
    :param status: 要设置的状态值，默认值为 "已发布"。
    :param timeout: 请求超时时间（秒），默认值为 6。
    :return: API 调用结果
    """
    url = "https://api.mingdao.com/v2/open/worksheet/editRows"
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
    }
    
    # 提取 rowid
    row_id = item.get("rowid")
    if not row_id:
        raise ValueError("未找到有效的 rowid 数据")  # 如果 rowid 不存在，直接抛出标准异常

    # 构建请求数据
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "rowIds": [row_id],  # 使用 rowid
        "controls": [
            {
                "controlId": "release_status",
                "value": status,
                "valueType": 1
            }
        ]
    }

    # 发起 POST 请求，设置超时
    try:
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        response.raise_for_status()  # 如果响应错误，抛出 HTTP 异常
    except requests.exceptions.RequestException as e:
        raise e  # 直接抛出请求异常，外部调用会处理

    # 检查响应状态码
    res = response.json()
    if res.get("success"):
        print(f"行记录 {row_id} 的状态已更新为: {status}")
        return res
    else:
        # 如果 API 返回失败，直接抛出异常
        error_message = res.get("error", "未知错误")
        raise ValueError(f"更新失败: {error_message}")



# 查询养号配置的方法
# ✅ 返回养号配置的整条记录（dict），如果没有返回 None
def get_raise_config_row(account_number: str):
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    headers = {
        "Content-Type": "application/json",
    }
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "raiseAccount",
        "filters": [
            {
                "controlId": "opreateAccount",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 1,
                "value": str(account_number)
            },
            {
                "controlId": "qudao",
                "dataType": 2,
                "value": 'bite'
            }

        ]
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            res = response.json()
            rows = res.get("data", {}).get("rows", [])
            if rows:
                return rows[0]  # 返回第一条记录
            else:
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        print(f"请求失败，异常信息: {e}")
        return None


def is_within_time_range(start_str, end_str):
    try:
        now = datetime.now().time()
        start = datetime.strptime(start_str, "%H:%M").time()
        end = datetime.strptime(end_str, "%H:%M").time()

        if start <= now <= end:
            return True
        else:
            return False
    except Exception as e:
        print(f"时间判断失败: {e}")
        return False


def like_post(driver):
    try:
        # 确保点赞按钮可点击，并滚动到目标元素
        like_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(@class, 'like-wrapper')]"))
        )
        
        # 点击点赞按钮
        like_button.click()  # 点击点赞按钮
        print("👍 点赞成功")
    except Exception as e:
        print(f"❌ 点赞失败: {e}")


def collect_post(driver):
    try:
        collect_button = driver.find_element(By.XPATH, "//span[contains(@class, 'collect-wrapper')]")
        collect_button.click()
        print("📌 收藏成功")
    except:
        print("❌ 收藏失败")


def get_random_comment(raise_config):
    comments_str = raise_config.get("commentContents", "")
    comments = [c.strip() for c in comments_str.strip().split("\n") if c.strip()]
    return random.choice(comments) if comments else "太棒啦～"

def follow_user(driver):
    try:
        # 等待按钮可点击
        follow_button = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//button[contains(@class, 'follow-button')]"))
        )

        # 使用 JavaScript 强制点击按钮，绕过一些可见性或遮挡问题
        driver.execute_script("arguments[0].click();", follow_button)
        
        print("✅ 点击了关注按钮")

    except Exception as e:
        print(f"❌ 关注失败: {e}")


def comment_post(driver, raise_config):
    try:
        # 先点击“说点什么...”区域
        comment_trigger = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'inner')]//span[contains(text(), '说点什么')]"))
        )
        driver.execute_script("arguments[0].click();", comment_trigger)
        time.sleep(1)

        # 等待 contenteditable 元素出现
        input_box = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//p[@contenteditable='true']"))
        )

        # 获取评论内容
        comments = raise_config.get("commentContents", "").strip().split("\n")
        comments = [c.strip() for c in comments if c.strip()]
        if not comments:
            print("⚠️ 配置中无有效评论内容")
            return

        comment_text = random.choice(comments)
        input_box.send_keys(comment_text)  # 在 contenteditable 元素中输入评论
        time.sleep(1)

        # 找到发送按钮
        send_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'submit')]"))
        )

        # 使用 ActionChains 确保按钮点击
        actions = ActionChains(driver)
        actions.move_to_element(send_button).click().perform()

        print(f"💬 评论成功：{comment_text}")

    except Exception:
        print("❌ 评论失败")



# def view_comments(driver):
#     try:
#         comment_area = driver.find_element(By.XPATH, "//div[contains(@class, 'comment') or contains(text(), '评论')]")
#         driver.execute_script("arguments[0].scrollIntoView(true);", comment_area)
#         print("👀 浏览评论区")
#     except:
#         print("❌ 评论区加载失败")

# def visit_profile(driver):
#     try:
#         profile_link = driver.find_element(By.XPATH, "//a[contains(@href, '/user/profile/')]")
#         driver.execute_script("arguments[0].scrollIntoView(true);", profile_link)
#         profile_link.click()
#         print("👤 访问用户主页")
#         time.sleep(random.uniform(4, 8))
#         driver.back()
#     except:
#         print("❌ 访问用户主页失败")



def run_raise_account_automation(driver, raise_config, browser_id, xhs_account=None, browser_name=None):
    print(f"🧬 开始执行养号流程：浏览器 ID {browser_id}")

    driver.get("https://www.xiaohongshu.com/explore")
    print("✅ 进入小红书首页")
    time.sleep(random.uniform(5, 10))

    # 访问小红书页面后检测登录状态
    # if not check_login_status(driver):
    #     print(f"🔐 访问首页后检测到浏览器 {browser_id} 未登录，停止养号操作")
    #     name = browser_name or f"浏览器_{browser_id}"
    #     account_name = xhs_account or "未知账号"
    #     handle_login_logout(browser_id, name, account_name)  # 暂时注释掉掉登检测
    #     return
   
    behavior_map = {
        "like": like_post,
        "collect": collect_post,
        "comment": lambda d: comment_post(d, raise_config),
        "follow": lambda d: follow_user(d),  # 新增关注行为
        # "view_comments": view_comments,
        # "visit_profile": visit_profile,
    }

    while is_within_time_range(raise_config.get("firstStartTime"), raise_config.get("firstEndTime")):
        try:
            # 获取所有笔记元素
            posts = driver.find_elements(By.XPATH, "//section[contains(@class, 'note-item')]")
            if not posts:
                print("⚠️ 没有找到笔记，稍后重试")
                time.sleep(random.uniform(10, 20))
                continue

            post = random.choice(posts)
            try:
                link = post.find_element(By.XPATH, ".//a[contains(@class, 'cover') and contains(@href, '/explore/')]")
                
                # 在进入笔记前加入点赞的随机概率
                if random.random() < 0.5:  # 50%的概率执行点赞
                    like_button = post.find_element(By.XPATH, ".//span[contains(@class, 'like-wrapper') and contains(@class, 'like-active')]")
                    like_button.click()
                    print("✅ 在进入笔记前点击了一个点赞")
                    time.sleep(random.uniform(2, 4))

                    # 点赞后检测是否出现二维码验证
                    if check_qrcode_verification(driver):
                        print(f"📱 点赞后检测到二维码验证弹窗，停止养号操作")
                        name = browser_name or f"浏览器_{browser_id}"
                        account_name = xhs_account or "未知账号"
                        handle_qrcode_verification(browser_id, name, account_name)
                        return

                driver.execute_script("arguments[0].scrollIntoView(true);", link)
                link.click()
                print("📝 成功进入随机笔记页面")
                time.sleep(random.uniform(5, 10))

                # 检测是否出现二维码验证
                if check_qrcode_verification(driver):
                    print(f"📱 检测到二维码验证弹窗，停止养号操作")
                    name = browser_name or f"浏览器_{browser_id}"
                    account_name = xhs_account or "未知账号"
                    handle_qrcode_verification(browser_id, name, account_name)
                    return

                # 执行随机行为
                random_behaviors = {
                    # "like": 0.6,
                    "collect": 0.3,
                    "comment": 0.2,
                    "follow": 0.1,  # 添加关注的概率
                    # "view_comments": 0.5,
                    # "visit_profile": 0.3
                }

                for behavior, prob in random_behaviors.items():
                    if random.random() < prob:
                        func = behavior_map.get(behavior)
                        if func:
                            print(f"🎯 执行行为: {behavior}")
                            func(driver)

                            # 执行行为后检测是否出现二维码验证
                            time.sleep(2)  # 等待可能的验证弹窗
                            if check_qrcode_verification(driver):
                                print(f"📱 执行 {behavior} 行为后检测到二维码验证弹窗，停止养号操作")
                                name = browser_name or f"浏览器_{browser_id}"
                                account_name = xhs_account or "未知账号"
                                handle_qrcode_verification(browser_id, name, account_name)
                                return

                        time.sleep(random.uniform(2, 4))

                # 停留一段时间模拟阅读
                stay_time = random.uniform(5, 15)
                print(f"⏱ 停留 {stay_time:.1f} 秒模拟阅读")
                time.sleep(stay_time)

                driver.back()
                time.sleep(random.uniform(3, 6))

            except Exception as e:
                print(f"⚠️ 进入笔记异常")

        except Exception as e:
            print(f"❌ 养号流程异常: {e}")

        # 每轮间隔几秒
        # interval = random.uniform(10, 20)
        # print(f"🔄 准备进入下一轮，等待 {interval:.1f} 秒")
        # time.sleep(interval)

    print("⏹ 当前时间不在配置范围内，停止养号流程")


def run_yanghao_automation(browser_id, browser, vps_mapping, raise_config, ip_name, xhs_account=None):
    # === 1. 获取 VPS 信息，判断是否需要更新 IP ===

    static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
    if ip_name in static_ips:
        print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
    else:
        vps_info = vps_mapping.get(ip_name)

        if not vps_info:
            print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
        else:
            current_ip = browser.get("lastIp", "")
            expected_ip = get_ppp0_ip(vps_info)  # 通过 SSH 获取 ppp0 的 IP

            if expected_ip and expected_ip != current_ip:
                print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")

                update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                time.sleep(2)

                closeBrowser(browser_id)
                time.sleep(15)
                print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")

                # 更新本地 browser_map 中的 IP 信息
                browser["lastIp"] = expected_ip
            else:
                print(f"当前 IP 未变更")

    try:
        res = open_browser(browser_id)
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        browser_name = browser.get("name", f"浏览器_{browser_id}")
        run_raise_account_automation(driver, raise_config, browser_id, xhs_account or ip_name, browser_name)
        closeBrowser(browser_id)
        driver.quit()
    except Exception as e:
        print(f"关闭浏览器 {browser_id}，错误信息：{e}")
        closeBrowser(browser_id)


# 创建浏览器并打开
def open_browser_with_id_and_run_automation(browser_map, browser_id, vps_mapping):
    browser = browser_map[browser_id]

    try:
        # 从备注中提取小红书号（格式如：8762500520_nara-重庆）
        xhs_remark = browser.get("remark")
        if "_" not in xhs_remark:
            print(f"备注格式错误，无法提取小红书号：{xhs_remark}")
            return

        xhs_account = xhs_remark.split("_")[0]
        print(f"提取到小红书账号: {xhs_account}")

        # ✅ 优先检查是否有发布任务
        row = check_fabu_config(browser)

        if row:
            print(f"📣 账号 {xhs_account} 检测到发布任务")

            # === 1. 获取 VPS 信息，判断是否需要更新 IP ===
            ip_name = row.get("ip_name")
            static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
            if ip_name in static_ips:
                print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
            else:
                vps_info = vps_mapping.get(ip_name)
                if not vps_info:
                    print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
                else:
                    current_ip = browser.get("lastIp", "")
                    expected_ip = get_ppp0_ip(vps_info)
                    if expected_ip and expected_ip != current_ip:
                        print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")
                        update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                        time.sleep(2)
                        closeBrowser(browser_id)
                        time.sleep(30)
                        print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
                        browser["lastIp"] = expected_ip
                    else:
                        print(f"当前 IP 未变更")

            # === 2. 打开浏览器并运行自动化 ===
            res = open_browser(browser_id)
            driver_path = res['data']['driver']
            debugger_address = res['data']['http']
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", debugger_address)
            chrome_service = Service(driver_path)
            driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

            browser_name = browser.get("name", f"浏览器_{browser_id}")
            run_automation(driver, row, browser_id, xhs_account, browser_name)

            # 发布完成后，进行矩阵统计
            print(f"📊 发布完成，开始进行矩阵统计...")
            try:
                # 进入创作者中心首页
                driver.get('https://creator.xiaohongshu.com/new/home')
                time.sleep(5)  # 等待页面加载

                # 调用矩阵统计函数
                account_matrix(driver)
                print(f"✅ 矩阵统计完成")

            except Exception as e:
                print(f"⚠️ 矩阵统计失败: {e}")

            closeBrowser(browser_id)
            return  # 发布和统计完成，直接 return

    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        closeBrowser(browser_id)


def get_existing_browser_map():
    """获取当前所有浏览器的完整信息映射"""
    browser_map = {}
    browser_data = get_browser_list(page=0, page_size=100)

    if not browser_data or not browser_data['success']:
        print("获取浏览器列表失败，程序退出。")
        time.sleep(10)
        return browser_map

    for browser in browser_data['data']['list']:
        browser_id = browser['id']
        browser_map[browser_id] = browser  # 直接存整个 browser 对象

    return browser_map


def extract_ip_from_account(account):
    """从账号配置中提取 IP 地址"""
    try:
        extra_data_list = json.loads(account.get('67d2bf436c09cd6ec047059b', '[]'))
        if isinstance(extra_data_list, list) and extra_data_list:
            first_item = extra_data_list[0]
            source_value = first_item.get("sourcevalue", "{}")
            source_data = json.loads(source_value)
            return source_data.get("678c76dc2e29d4dda9f251b6", None)  # 最新 IP
    except json.JSONDecodeError:
        print(f"账号 {account.get('zhmc', '未知')} 的数据解析失败")
    return None

def create_missing_browsers(account_configs, vps_mapping):
    """创建未创建的浏览器，返回 browser_id -> ip 映射"""
    browser_ip_map = {}

    for account in account_configs:
        bite_window_status = account.get('bite_window_status', '')

        ip_address = extract_ip_from_account(account)

        if bite_window_status == '未创建' and ip_address:
            
            name = account.get("ip_name")
            vps_info = vps_mapping.get(name)
            if not vps_info:
                print(f"⚠️ 未找到 {name} 的 VPS 配置，跳过")
                continue

            browser_id = createBrowser(ip_address, account, vps_info)
            if browser_id:
                print(f"✅ 已创建浏览器 ID {browser_id}，IP: {ip_address}")
                update_browser_status(account, browser_id, "创建成功")
                browser_ip_map[browser_id] = ip_address  # 记录新创建的浏览器 IP
            else:
                print(f"❌ 创建浏览器失败，IP: {ip_address}")

    return browser_ip_map


def fetch_logged_in_browser_ids(account_configs, browser_map):
    """获取已登录的浏览器id"""
    browser_ids = []

    for account in account_configs:
        browser_id = account.get("bite_window_id")
        if not browser_id or browser_id not in browser_map:
            continue

        # 只有当 bite_window_status 为 "已登录" 时，才添加到 browser_ids
        if account.get("bite_window_status") == "已登录":
            browser_ids.append(browser_id)

    return browser_ids


def detect_and_update_browser_ips(account_configs, browser_map, vps_mapping):
    """检测 IP 变更并更新代理"""
    updated_browser_map = browser_map.copy()
    browser_ids = []

    for account in account_configs:
        browser_id = account.get("bite_window_id")
        if not browser_id or browser_id not in browser_map:
            continue

        name = account.get("ip_name")
        vps_info = vps_mapping.get(name)
        if not vps_info:
            print(f"⚠️ 未找到 {name} 的 VPS 配置，跳过")
            continue

        browser = browser_map[browser_id]  # 获取完整的浏览器对象
        current_ip = browser.get("lastIp", "")  # 从浏览器对象获取当前 IP
        # expected_ip = extract_ip_from_account(account)  # 获取最新的 IP

        expected_ip = get_ppp0_ip(vps_info)

        if expected_ip and expected_ip != current_ip:
            print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")

            update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
            time.sleep(2)

            closeBrowser(browser_id)
            time.sleep(30)
            print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
            # openBrowser(browser_id)
            # print(f"✅ 浏览器 ID {browser_id} 已重启，应用新 IP {expected_ip}")

            # 更新浏览器对象中的 IP（这里假设浏览器对象是个可变字典）
            updated_browser_map[browser_id]["lastIp"] = expected_ip

        # 只有当 bite_window_status 为 "已登录" 时，才添加到 browser_ids
        if account.get("bite_window_status") == "已登录":
            browser_ids.append(browser_id)

    return browser_ids



def run_automation_concurrently(browser_ids, browser_map, vps_mapping, max_workers=3, delay=8):
    """最多 max_workers 并发，任务启动间隔 delay 秒"""
    if not browser_ids:
        print("⚠️ 没有需要执行的浏览器，等待 30 秒后重试")
        time.sleep(30)
        return

    print(f"🚀 启动自动化任务，总数 {len(browser_ids)}，最大并发 {max_workers}，每个延迟 {delay}s")

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for browser_id in browser_ids:
            # 将 delay 作为参数传进线程里，错峰启动
            future = executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, vps_mapping)
            futures.append(future)
            time.sleep(5) 

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"❌ 自动化任务出错: {e}")

    print("✅ 所有自动化任务完成")



# def run_automation_concurrently(browser_ids, browser_map):
#     """使用并发执行自动化，每个线程间隔 8 秒启动"""
#     if not browser_ids:
#         print("未找到已登录的浏览器 ID，程序等待 60 秒后重试。")
#         time.sleep(60)
#         return

#     batch_size = 3  # 每次 3 个线程

#     for i in range(0, len(browser_ids), batch_size):
#         batch = browser_ids[i:i + batch_size]

#         with concurrent.futures.ThreadPoolExecutor(max_workers=len(batch)) as executor:
#             future_to_browser_id = {
#                 executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, index * 8): browser_id
#                 for index, browser_id in enumerate(batch)
#             }

#             for future in concurrent.futures.as_completed(future_to_browser_id):
#                 browser_id = future_to_browser_id[future]
#                 try:
#                     future.result()
#                     print(f"✅ 浏览器 ID {browser_id} 自动化操作执行完毕")
#                 except Exception as e:
#                     print(f"❌ 浏览器 ID {browser_id} 执行自动化操作时出错: {e}")

#         # 等待所有线程执行完毕后再进入下一批次
#         time.sleep(8 * batch_size)

MINGDAO_API = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
MINGDAO_HEADERS = {
    "Content-Type": "application/json"
}
MINGDAO_PAYLOAD = {
    "appKey": "f08bf7f7cfe8c038",
    "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
    "worksheetId": "proxy_ip",
    "controls": [],
    "filters": [],
    "pageSize": 1000,
    "pageIndex": 1
}

def fetch_vps_mapping():
    resp = requests.post(MINGDAO_API, json=MINGDAO_PAYLOAD, headers=MINGDAO_HEADERS)
    data = resp.json()["data"]["rows"]
    mapping = {}
    for row in data:
        name = row.get("name")
        server_ip = row.get("server_ip")
        server_port = row.get("server_port")

        # 只有当 server_ip 和 server_port 都有值时才添加到mapping
        if not server_ip or not server_port:
            print(f"⚠️ VPS {name} 缺少必要信息 (IP: {server_ip}, Port: {server_port})，跳过")
            continue

        # 安全地转换 server_port 为整数
        try:
            server_port = int(server_port)
        except (ValueError, TypeError):
            print(f"⚠️ VPS {name} 的端口号无效: {server_port}，跳过")
            continue

        mapping[name] = {
            "server_ip": server_ip,
            "server_port": server_port,
            "password": row.get("password"),
            "username": "root",
            "wsport": row.get("wsport"),
        }
    return mapping

def get_ppp0_ip(info):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=info["server_ip"],
            port=info["server_port"],
            username=info["username"],
            password=info["password"]
        )
        command = "ip addr show ppp0 | grep inet | awk '{print $2}' | cut -d/ -f1"
        _, stdout, stderr = ssh.exec_command(command)

        result = stdout.read().decode().strip()
        err = stderr.read().decode().strip()

        ssh.close()

        if result:
            print(f"✅ 成功获取 ppp0 IP：{result}")
            return result
        else:
            print("⚠️ 没有获取到 IP，可能 ppp0 接口未启用")
            if err:
                print("错误信息：", err)
            return None

    except Exception as e:
        print(f"❌ SSH 连接失败：{e}")
        return None

def check_manual_statistics_tasks(bite_user_name):
    """检查是否有手动统计任务"""
    try:
        # 查询明道云中状态为"待执行"的统计任务，条件：待执行 + 已登录 + 有 bite_window_id + 指定比特用户名
        query_payload = {
            "appKey": "f08bf7f7cfe8c038",
            "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
            "worksheetId": "account_config",
            "pageSize": 1000,
            "pageIndex": 1,
            "filters": [
                {
                    "controlId": "statsStatus",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 2,
                    "value": "待执行"
                },
                {
                    "controlId": "bite_window_status",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 2,
                    "value": "已登录"
                },
                {
                    "controlId": "bite_window_id",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 8
                },
                {
                    "controlId": "bite_user_name",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 2,
                    "value": bite_user_name
                }
            ]
        }

        response = requests.post(
            "https://api.mingdao.com/v2/open/worksheet/getFilterRows",
            json=query_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("success") and result.get("data", {}).get("rows"):
                manual_tasks = []
                for row in result["data"]["rows"]:
                    browser_id = row.get("bite_window_id", "")
                    xhs_account = row.get("id", "")  # 小红书账号
                    row_id = row.get("rowid", "")

                    if browser_id and xhs_account:
                        manual_tasks.append({
                            "browser_id": browser_id,
                            "xhs_account": xhs_account,
                            "row_id": row_id
                        })

                if manual_tasks:
                    print(f"🎯 发现 {len(manual_tasks)} 个待执行的统计任务（用户: {bite_user_name}）")
                    return manual_tasks

        return []

    except Exception as e:
        print(f"❌ 检查手动统计任务失败: {e}")
        return []

def update_statistics_status(row_id, status):
    """更新统计状态"""
    try:
        update_payload = {
            "appKey": "f08bf7f7cfe8c038",
            "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
            "worksheetId": "account_config",
            "rowIds": [row_id],
            "controls": [
                {
                    "controlId": "statsStatus",
                    "value": status,
                    "valueType": 1
                }
            ]
        }

        response = requests.post(
            "https://api.mingdao.com/v2/open/worksheet/editRows",
            json=update_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ 已更新统计状态为: {status}")
                return True

        print(f"⚠️ 更新统计状态失败")
        return False

    except Exception as e:
        print(f"❌ 更新统计状态异常: {e}")
        return False

def execute_manual_statistics_tasks(manual_tasks, vps_mapping, browser_map):
    """执行手动统计任务"""
    print(f"🎯 开始执行 {len(manual_tasks)} 个手动统计任务")

    success_count = 0

    for i, task in enumerate(manual_tasks):
        try:
            browser_id = task["browser_id"]
            xhs_account = task["xhs_account"]
            row_id = task["row_id"]

            print(f"\n📊 [{i+1}/{len(manual_tasks)}] 执行手动统计: 浏览器 {browser_id}, 账号 {xhs_account}")

            # 获取浏览器信息
            browser_info = browser_map.get(browser_id, {})
            browser_name = browser_info.get('name', f'浏览器_{browser_id}')

            # 执行统计（复用现有的统计逻辑）
            success = execute_single_statistics(browser_id, browser_info, browser_name, xhs_account, vps_mapping, row_id)

            if success:
                success_count += 1
                print(f"✅ 浏览器 {browser_id} 手动统计完成")
                # 更新状态为执行成功
                update_statistics_status(row_id, "执行成功")
            else:
                print(f"❌ 浏览器 {browser_id} 手动统计失败")
                # 更新状态为执行失败
                update_statistics_status(row_id, "执行失败")

        except Exception as e:
            print(f"❌ 执行手动统计任务失败: {e}")
            # 更新状态为执行失败
            if 'row_id' in locals():
                update_statistics_status(row_id, "执行失败")

    print(f"🎯 手动统计任务完成，成功: {success_count}/{len(manual_tasks)}")
    return success_count > 0

def execute_single_statistics(browser_id, browser_info, browser_name, xhs_account, vps_mapping, row_id):
    """执行单个浏览器的统计任务"""
    try:
        # === 1. 获取 VPS 信息，判断是否需要更新 IP ===
        xhs_remark = browser_info.get('remark', '未知账号')
        ip_name = None
        if "_" in xhs_remark:
            ip_name = xhs_remark.split("_", 1)[1]

        if ip_name:
            static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
            if ip_name in static_ips:
                print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
            else:
                vps_info = vps_mapping.get(ip_name)
                if not vps_info:
                    print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
                else:
                    current_ip = browser_info.get("lastIp", "")
                    expected_ip = get_ppp0_ip(vps_info)
                    if expected_ip and expected_ip != current_ip:
                        print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")
                        update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                        time.sleep(2)
                        closeBrowser(browser_id)
                        time.sleep(30)
                        print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
                        browser_info["lastIp"] = expected_ip
                    else:
                        print(f"📍 当前 IP 未变更: {current_ip}")

        # 启动浏览器
        result = open_browser(browser_id)
        if result["success"]:
            # 连接到浏览器
            driver_path = result['data']['driver']
            debugger_address = result['data']['http']
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", debugger_address)
            chrome_service = Service(driver_path)
            driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

            # 执行统计
            collect_notes_statistics(driver, browser_id, browser_name, xhs_account)
            driver.quit()

            # 关闭浏览器
            closeBrowser(browser_id)
            return True
        else:
            print(f"❌ 启动浏览器 {browser_id} 失败: {result['message']}")
            return False

    except Exception as e:
        print(f"❌ 执行单个统计任务失败: {e}")
        return False

def initialize_system(bite_user_name):
    """初始化系统：获取配置、创建浏览器、获取映射"""
    print("🔧 初始化系统...")

    # VPS 信息映射
    vps_mapping = fetch_vps_mapping()

    # 账号配置
    account_configs = fetch_account_config(bite_user_name)

    # 1. 创建未创建的浏览器
    create_missing_browsers(account_configs, vps_mapping)

    # 2. 获取所有浏览器 ID -> 浏览器对象的完整映射
    browser_map = get_existing_browser_map()

    # 3. 获取登录的浏览器
    browser_ids = fetch_logged_in_browser_ids(account_configs, browser_map)

    return vps_mapping, account_configs, browser_map, browser_ids

def run_publish_tasks(bite_user_name):
    """执行发布任务"""
    try:
        vps_mapping, account_configs, browser_map, browser_ids = initialize_system(bite_user_name)

        # 4. 执行自动化操作
        run_automation_concurrently(browser_ids, browser_map, vps_mapping)

        print("✅ 所有自动化任务执行完毕")
        return True
    except Exception as e:
        print(f"⚠️ 发布任务异常: {e}")
        return False



def run_statistics_for_all_accounts():
    """为所有账号执行统计任务"""
    print("🕛 开始执行定时统计任务...")

    try:
        # 获取VPS映射和浏览器映射（统计任务不需要完整初始化）
        vps_mapping = fetch_vps_mapping()
        browser_map = get_existing_browser_map()

        if not browser_map:
            print("⚠️ 没有找到可用的浏览器")
            return

        print(f"📊 开始统计 {len(browser_map)} 个浏览器的数据")

        # 为每个浏览器执行统计
        for browser_id, browser_info in browser_map.items():
            try:
                browser_name = browser_info.get('name', f'浏览器_{browser_id}')
                xhs_remark = browser_info.get('remark', '未知账号')

                # 提取小红书账号（只要下划线前的部分）
                if "_" in xhs_remark:
                    xhs_account = xhs_remark.split("_")[0]
                else:
                    xhs_account = xhs_remark

                print(f"📊 开始统计浏览器 {browser_id} ({browser_name}) 的数据...")

                # === 1. 获取 VPS 信息，判断是否需要更新 IP ===
                # 从备注中提取IP名称（假设格式为：账号_IP名称）
                ip_name = None
                if "_" in xhs_remark:
                    ip_name = xhs_remark.split("_", 1)[1]

                if ip_name:
                    static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
                    if ip_name in static_ips:
                        print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
                    else:
                        vps_info = vps_mapping.get(ip_name)
                        if not vps_info:
                            print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
                        else:
                            current_ip = browser_info.get("lastIp", "")
                            expected_ip = get_ppp0_ip(vps_info)
                            if expected_ip and expected_ip != current_ip:
                                print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")
                                update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                                time.sleep(2)
                                closeBrowser(browser_id)
                                time.sleep(30)
                                print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
                                browser_info["lastIp"] = expected_ip
                            else:
                                print(f"📍 当前 IP 未变更: {current_ip}")

                # 启动浏览器
                result = open_browser(browser_id)
                if result["success"]:
                    # 连接到浏览器
                    driver_path = result['data']['driver']
                    debugger_address = result['data']['http']
                    chrome_options = webdriver.ChromeOptions()
                    chrome_options.add_experimental_option("debuggerAddress", debugger_address)
                    chrome_service = Service(driver_path)
                    driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

                    # 执行统计
                    collect_notes_statistics(driver, browser_id, browser_name, xhs_account)
                    driver.quit()

                    # 关闭浏览器
                    closeBrowser(browser_id)
                else:
                    print(f"❌ 启动浏览器 {browser_id} 失败: {result['message']}")

            except Exception as e:
                print(f"❌ 统计浏览器 {browser_id} 数据失败: {e}")

        print("✅ 所有账号统计任务完成")

    except Exception as e:
        print(f"❌ 执行统计任务失败: {e}")



def main(bite_user_name=None, platform=None):
    _ = platform  # 标记参数已使用

    if bite_user_name is None:
        bite_user_name = input("请输入启动用户名: ")

    # 询问运行模式
    print("\n🚀 请选择运行模式:")
    print("1. 发布模式 (默认) - 自动检查手动统计任务")
    print("2. 统计模式 (立即执行一次)")
    print("3. 定时统计模式 (每天0点执行)")
    print("4. 发布+定时统计模式 - 自动检查手动统计任务")

    mode = input("请输入模式编号 (1-4，默认1): ").strip()

    if mode == "2":
        # 仅执行统计
        print("📊 执行统计模式...")
        run_statistics_for_all_accounts()
        return
    elif mode == "3":
        # 仅定时统计（循环模式）
        print("⏰ 启动定时统计模式，每天0点执行...")
        while True:
            current_time = datetime.now()
            if current_time.hour == 0 and current_time.minute < 5:
                print("🕛 到达统计时间，开始执行统计任务...")
                run_statistics_for_all_accounts()
                print("✅ 统计任务完成，等待下次执行...")
                time.sleep(300)  # 等待5分钟避免重复执行
            else:
                time.sleep(60)  # 每分钟检查一次时间
    elif mode == "4":
        # 发布+定时统计
        print("🔄 启动发布+定时统计模式...")
        print("📅 每天0点执行统计任务，其他时间执行发布任务")


    # 主循环
    while True:
        try:
            # 检查是否到了统计时间（每天0点）
            current_time = datetime.now()
            if mode == "4" and current_time.hour == 0 and current_time.minute < 5:
                print("🕛 到达统计时间，开始执行统计任务...")
                run_statistics_for_all_accounts()
                print("✅ 统计任务完成，继续发布任务...")
                # 等待5分钟，避免重复执行统计
                time.sleep(300)
                continue

            # 优先检查手动统计任务
            manual_tasks = check_manual_statistics_tasks(bite_user_name)
            if manual_tasks:
                print(f"🎯 发现手动统计任务，暂停其他操作执行统计...")

                # 获取系统信息
                vps_mapping = fetch_vps_mapping()
                browser_map = get_existing_browser_map()

                # 执行手动统计任务
                execute_manual_statistics_tasks(manual_tasks, vps_mapping, browser_map)
                print("✅ 手动统计任务完成，继续其他任务...")
                time.sleep(5)
                continue

            # 执行发布任务
            success = run_publish_tasks(bite_user_name)
            if success:
                print("✅ 发布任务完成，等待 5 秒后重新执行")
            else:
                print("⚠️ 发布任务失败，等待 5 秒后重试")

        except Exception as e:
            print(f"⚠️ 主循环异常: {e}")

        # 等待后继续
        time.sleep(5)

def set_date_input_via_js(driver, input_element, date_str):
    """通过 JS 设置日期输入框的值并触发 input 和 change 事件"""
    driver.execute_script("""
        const input = arguments[0];
        const value = arguments[1];
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
    """, input_element, date_str)

def set_date_range_and_export(driver):
    """设置时间范围并导出数据"""
    try:
        print("🕒 开始设置时间范围...")

        wait = WebDriverWait(driver, 10)

        # 计算时间范围（最近 5 个月）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=150)

        start_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")

        print(f"📅 设置时间范围: {start_date_str} 到 {end_date_str}")

        try:
            # 找到所有带日期的 input 框（Element UI 是 range-input 两个）
            date_inputs = wait.until(EC.presence_of_all_elements_located(
                (By.XPATH, "//input[contains(@class, 'el-range-input')]")
            ))

            if len(date_inputs) >= 2:
                start_input = date_inputs[0]
                end_input = date_inputs[1]

                # 用 JS 设置值
                set_date_input_via_js(driver, start_input, start_date_str)
                time.sleep(0.5)
                set_date_input_via_js(driver, end_input, end_date_str)
                time.sleep(1)

                print("✅ 时间范围设置成功")
            else:
                raise Exception("未找到两个日期输入框")

        except Exception as e:
            print(f"⚠️ 设置时间范围失败: {e}")
            print("➡️ 使用默认时间范围继续")

        # 点击“导出数据”按钮
        try:
            export_button = wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(@class, 'el-button') and contains(., '导出数据')]")
            ))

            print("📤 点击导出数据按钮...")
            export_button.click()

            print("✅ 导出请求已发送，请等待下载开始...")
            time.sleep(5)

            return True

        except Exception as e:
            print(f"❌ 点击导出按钮失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 整体流程失败: {e}")
        return False


def extract_overview_data(driver):
    """基于导出数据的方式获取统计信息"""
    try:
        print("� 开始通过导出方式获取数据...")

        # 设置时间范围并导出数据
        if set_date_range_and_export(driver):
            print("✅ 数据导出成功")

            # 这里可以添加处理下载文件的逻辑
            # 由于导出是异步的，我们先返回一个标识
            return {
                'export_requested': True,
                'export_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'date_range': '最近5个月'
            }
        else:
            print("❌ 数据导出失败")
            return {}

    except Exception as e:
        print(f"❌ 提取数据失败：{e}")
        return {}

def parse_number(text):
    """解析数字文本，处理'-'和空值"""
    if not text or text == '-' or text == '':
        return 0

    # 移除空格
    text = text.strip()

    # 处理数字
    try:
        # 如果是纯数字
        if text.isdigit():
            return int(text)

        # 处理带单位的数字
        import re

        # 匹配万、k等单位
        match = re.match(r'(\d+\.?\d*)[万wW]', text)
        if match:
            return int(float(match.group(1)) * 10000)

        match = re.match(r'(\d+\.?\d*)[kK]', text)
        if match:
            return int(float(match.group(1)) * 1000)

        # 匹配纯数字（包含小数点）
        match = re.match(r'(\d+\.?\d*)', text)
        if match:
            return int(float(match.group(1)))

    except (ValueError, AttributeError):
        pass

    return 0

def get_browser_download_path():
    """获取浏览器的下载路径"""
    import platform
    import os

    system = platform.system()
    username = os.getenv('USERNAME') or os.getenv('USER')

    # 常见的下载路径
    possible_paths = []

    if system == "Windows":
        possible_paths = [
            os.path.join(os.path.expanduser("~"), "Downloads"),
            f"C:\\Users\\<USER>\\Downloads",
            os.path.join(os.path.expanduser("~"), "下载"),
        ]
    elif system == "Darwin":  # macOS
        possible_paths = [
            os.path.join(os.path.expanduser("~"), "Downloads"),
        ]
    else:  # Linux
        possible_paths = [
            os.path.join(os.path.expanduser("~"), "Downloads"),
            os.path.join(os.path.expanduser("~"), "下载"),
        ]

    # 返回第一个存在的路径
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果都不存在，返回默认路径
    return os.path.join(os.path.expanduser("~"), "Downloads")

def process_downloaded_csv_and_upload_to_mingdao(download_folder=None, xhs_number=None, nickname=None, csv_file_path=None):
    """处理下载的CSV文件并上传到明道云"""
    try:
        if csv_file_path and os.path.exists(csv_file_path):
            print(f"📄 使用指定的CSV文件: {csv_file_path}")
            latest_csv = csv_file_path
        else:
            # 如果没有指定下载文件夹，自动获取
            if not download_folder:
                download_folder = get_browser_download_path()

            print(f"🔍 在下载文件夹中查找Excel文件: {download_folder}")

            # 检查下载文件夹是否存在
            if not os.path.exists(download_folder):
                print(f"❌ 下载文件夹不存在: {download_folder}")
                return False

            # 查找小红书导出的文件（支持多种可能的文件名）

            found_files = []

            # 扫描下载文件夹
            print("📋 扫描下载文件夹中的文件:")
            for file in os.listdir(download_folder):
                if file.endswith(('.xlsx', '.csv')):
                    file_path = os.path.join(download_folder, file)
                    file_time = os.path.getmtime(file_path)
                    file_time_str = datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"   - {file} (修改时间: {file_time_str})")

                    # 检查是否匹配目标模式
                    is_target = False
                    if "笔记" in file or "内容分析" in file or "数据" in file:
                        is_target = True

                    found_files.append((file_path, file_time, is_target))

            if not found_files:
                print("❌ 下载文件夹中没有找到任何Excel或CSV文件")
                return False

            # 优先选择匹配目标模式的文件
            target_files = [f for f in found_files if f[2]]  # 第三个元素是is_target

            if target_files:
                # 选择最新的目标文件
                latest_csv = max(target_files, key=lambda x: x[1])[0]
                print(f"✅ 找到匹配的文件: {os.path.basename(latest_csv)}")
            else:
                # 如果没有匹配的，选择最新的任意文件
                latest_csv = max(found_files, key=lambda x: x[1])[0]
                print(f"⚠️ 未找到明确匹配的文件，使用最新文件: {os.path.basename(latest_csv)}")

                # 询问用户确认
                confirm = input("是否使用此文件? (y/n，直接回车默认为y): ").strip().lower()
                if confirm and confirm not in ['y', 'yes', '']:
                    print("❌ 用户取消操作")
                    return False

        # 读取Excel或CSV文件
        try:
            import pandas as pd

            if latest_csv.endswith('.xlsx'):
                # 读取Excel文件，跳过第一行（因为第一行是重复的标题）
                df = pd.read_excel(latest_csv, header=1)  # 使用第二行作为表头
                print(f"✅ 成功读取Excel文件")

                # 调试信息：显示列名和数据结构
                print(f"📊 Excel文件信息:")
                print(f"   - 行数: {len(df)}")
                print(f"   - 列数: {len(df.columns)}")
                print(f"   - 列名: {list(df.columns)}")

                if len(df) > 0:
                    print(f"📋 前3行数据预览:")
                    for i, (_, row) in enumerate(df.head(3).iterrows()):
                        print(f"   第{i+1}行: {dict(row)}")
                else:
                    print("⚠️ Excel文件为空")

            else:
                # 读取CSV文件
                for encoding in ['utf-8', 'gbk', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(latest_csv, encoding=encoding)
                        print(f"✅ 成功读取CSV文件，编码: {encoding}")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    print("❌ 无法读取CSV文件，编码问题")
                    return False

        except ImportError:
            print("⚠️ pandas未安装，使用基础读取方式")
            if latest_csv.endswith('.xlsx'):
                print("❌ 无法读取Excel文件，需要安装pandas和openpyxl")
                return False
            else:
                # 使用基础CSV读取
                with open(latest_csv, 'r', encoding='utf-8-sig') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)

                # 转换为类似pandas的格式
                df = rows

        # 处理数据并上传到明道云
        upload_count = 0

        if isinstance(df, list):  # 基础CSV读取的情况
            for row in df:
                if upload_note_data_to_mingdao(row, xhs_number, nickname):
                    upload_count += 1
        else:  # pandas读取的情况
            for _, row in df.iterrows():
                if upload_note_data_to_mingdao(row.to_dict(), xhs_number, nickname):
                    upload_count += 1

        print(f"✅ 成功上传 {upload_count} 条笔记数据到明道云")
        return True

    except Exception as e:
        print(f"❌ 处理CSV文件失败: {e}")
        return False

def upload_note_data_to_mingdao(note_data, xhs_number, nickname):
    """将单条笔记数据上传到明道云"""
    try:
        # 调试信息：显示原始数据
        print(f"🔍 处理数据行: {note_data}")

        # 提取笔记信息（根据实际的Excel列名）
        title = note_data.get('笔记标题', note_data.get('title', ''))
        view_count = note_data.get('观看量', note_data.get('观看', note_data.get('views', '0')))
        like_count = note_data.get('点赞', note_data.get('likes', '0'))
        comment_count = note_data.get('评论', note_data.get('comments', '0'))
        collection_count = note_data.get('收藏', note_data.get('collections', '0'))
        share_count = note_data.get('分享', note_data.get('shares', '0'))
        # fans_growth = note_data.get('涨粉', note_data.get('fans_growth', '0'))  # 暂时不使用

        print(f"📝 提取的数据: 标题='{title}', 观看={view_count}, 点赞={like_count}")

        if not title:
            print("⚠️ 标题为空，跳过此行")
            return False

        # 检查是否已存在
        check_payload = {
            "appKey": APP_KEY,
            "sign": SIGN,
            "worksheetId": BJK_WORKSHEET_ID,
            "pageSize": 100,
            "pageIndex": 1,
            "filters": [
                {"controlId": "xhsh", "dataType": 2, "spliceType": 1, "filterType": 2, "value": xhs_number},
                {"controlId": "bjbt", "dataType": 2, "spliceType": 1, "filterType": 2, "value": title}
            ]
        }

        check_res = requests.post("https://api.mingdao.com/v2/open/worksheet/getFilterRows", json=check_payload)
        row_id = None
        if check_res.status_code == 200:
            rows = check_res.json().get("data", {}).get("rows", [])
            if rows:
                row_id = rows[0]["rowid"]

        # 构建字段
        controls = [
            {"controlId": "bjbt", "value": title},
            {"controlId": "xiaoyanjing", "value": str(view_count)},
            {"controlId": "pinglun", "value": str(comment_count)},
            {"controlId": "dianzan", "value": str(like_count)},
            {"controlId": "shoucang", "value": str(collection_count)},
            {"controlId": "zhuanfa", "value": str(share_count)},
            {"controlId": "xiaoyanjing_number", "value": parse_count(str(view_count))},
            {"controlId": "pinglun_number", "value": parse_count(str(comment_count))},
            {"controlId": "dianzan_number", "value": parse_count(str(like_count))},
            {"controlId": "shoucang_number", "value": parse_count(str(collection_count))},
            {"controlId": "zhuanfa_number", "value": parse_count(str(share_count))},
            {"controlId": "xhsh", "value": xhs_number},
            {"controlId": "nicheng", "value": nickname}
        ]

        if row_id:
            # 更新笔记
            payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": BJK_WORKSHEET_ID,
                "rowId": row_id,
                "triggerWorkflow": True,
                "controls": controls
            }
            res = requests.post("https://api.mingdao.com/v2/open/worksheet/editRow", json=payload)
            success = res.status_code == 200
            print(f"📝 {'已更新' if success else '更新失败'}: {title}")
        else:
            # 新增笔记
            payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": BJK_WORKSHEET_ID,
                "triggerWorkflow": True,
                "controls": controls
            }
            res = requests.post("https://api.mingdao.com/v2/open/worksheet/addRow", json=payload)
            success = res.status_code == 200
            print(f"✅ {'已新增' if success else '新增失败'}: {title}")

        return success

    except Exception as e:
        print(f"⚠️ 上传笔记数据失败: {e}")
        return False

def save_statistics_to_csv(data, account_name):
    """保存统计数据到CSV文件"""
    try:
        # 创建统计文件夹
        stats_folder = os.path.join(os.path.expanduser("~"), "Desktop", "xhs_statistics")
        os.makedirs(stats_folder, exist_ok=True)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{account_name}_{timestamp}_export_log.csv"
        filepath = os.path.join(stats_folder, filename)

        # 准备数据
        csv_data = [
            ["指标", "数值"],
            ["账号名称", account_name],
            ["导出时间", data.get('export_time', '')],
            ["时间范围", data.get('date_range', '')],
            ["导出状态", "成功" if data.get('export_requested') else "失败"]
        ]

        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(csv_data)

        print(f"✅ 导出日志已保存到: {filepath}")
        return filepath

    except Exception as e:
        print(f"❌ 保存导出日志失败: {e}")
        return None

def run_statistics_for_browser(browser_id):
    """为指定浏览器运行统计功能"""
    try:
        print(f"🚀 开始为浏览器 {browser_id} 运行统计...")

        # 打开浏览器
        res = open_browser(browser_id)
        if not res or 'data' not in res:
            print(f"❌ 无法打开浏览器 {browser_id}")
            return False

        # 获取driver连接信息
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        # 创建Chrome driver
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        # 获取账号信息
        account_name = get_account_number(driver)
        if not account_name:
            account_name = f"browser_{browser_id}"

        print(f"📊 当前账号: {account_name}")

        # 导航到内容分析页面
        print("🔄 导航到内容分析页面...")
        driver.get("https://creator.xiaohongshu.com/creator/data/content")
        time.sleep(5)

        # 检查是否需要登录
        # if check_login_status(driver):  # 暂时注释掉掉登检测
        #     print("❌ 账号未登录，请先登录")
        #     return False

        # 提取统计数据
        print("📈 开始提取统计数据...")
        data = extract_overview_data(driver)

        if data:
            # 保存数据到CSV
            csv_file = save_statistics_to_csv(data, account_name)
            if csv_file:
                print(f"✅ 统计完成，数据已保存到: {csv_file}")
                return True
        else:
            print("⚠️ 未能提取到有效数据")
            return False

    except Exception as e:
        print(f"❌ 统计过程中发生错误: {e}")
        return False

def run_statistics_for_all_browsers():
    """为所有登录的浏览器运行统计功能"""
    try:
        print("🔍 获取所有浏览器...")

        # 获取浏览器映射
        browser_map = get_existing_browser_map()
        if not browser_map:
            print("❌ 未找到任何浏览器")
            return

        print(f"📊 找到 {len(browser_map)} 个浏览器")

        # 为每个浏览器运行统计
        success_count = 0
        for browser_id in browser_map.keys():
            print(f"\n{'='*50}")
            print(f"处理浏览器 {browser_id}")
            print(f"{'='*50}")

            if run_statistics_for_browser(browser_id):
                success_count += 1

            # 等待一段时间再处理下一个浏览器
            time.sleep(3)

        print(f"\n✅ 统计完成！成功处理 {success_count}/{len(browser_map)} 个浏览器")

    except Exception as e:
        print(f"❌ 批量统计过程中发生错误: {e}")

def test_statistics_only():
    """独立测试统计功能 - 不需要分发流程"""
    print("🧪 开始独立测试统计功能...")

    try:
        # 获取可用的浏览器
        browser_map = get_existing_browser_map()
        if not browser_map:
            print("❌ 未找到任何浏览器，请先创建浏览器")
            return

        print(f"📊 找到 {len(browser_map)} 个浏览器")

        # 让用户选择浏览器
        browser_ids = list(browser_map.keys())
        print("\n可用的浏览器:")
        for i, browser_id in enumerate(browser_ids):
            print(f"  {i+1}. 浏览器 {browser_id}")

        choice = input(f"\n请选择浏览器 (1-{len(browser_ids)}) 或按回车选择第一个: ").strip()

        if choice == "":
            selected_browser_id = browser_ids[0]
        else:
            try:
                index = int(choice) - 1
                if 0 <= index < len(browser_ids):
                    selected_browser_id = browser_ids[index]
                else:
                    print("❌ 无效选择，使用第一个浏览器")
                    selected_browser_id = browser_ids[0]
            except ValueError:
                print("❌ 无效输入，使用第一个浏览器")
                selected_browser_id = browser_ids[0]

        print(f"🎯 选择浏览器: {selected_browser_id}")

        # 打开浏览器并设置下载路径
        print("🔄 正在连接浏览器...")
        driver, download_path = create_chrome_driver_with_download_path(selected_browser_id)
        if not driver:
            print(f"❌ 无法打开浏览器 {selected_browser_id}")
            return

        print("✅ 浏览器连接成功")

        # 获取账号信息用于明道云上传
        driver.get("https://creator.xiaohongshu.com/new/home")
        try:
            # 先获取小红书号和昵称
            xhs_number, nickname = account_matrix(driver)
            print(f"📊 账号信息: {nickname} ({xhs_number})")
        except Exception as e:
            print(f"⚠️ 获取账号信息失败: {e}")

        # 直接访问内容分析页面
        print("🔄 导航到内容分析页面...")
        driver.get("https://creator.xiaohongshu.com/statistics/data-analysis")
        time.sleep(5)

        # 设置时间范围并导出数据
        print("📈 开始设置时间范围并导出数据...")
        export_success = set_date_range_and_export(driver)

        if export_success:
            print("✅ 数据导出请求成功！")

            # 等待下载完成
            print("⏳ 等待下载完成...")
            time.sleep(10)  # 给下载一些时间

            # 处理下载的文件并上传到明道云
            print("📤 开始处理下载文件并上传到明道云...")
            if process_downloaded_csv_and_upload_to_mingdao(download_path, xhs_number, nickname):
                print("✅ 数据已成功上传到明道云！")
            else:
                print("⚠️ 数据上传到明道云失败")

            # 保存导出日志
            log_data = {
                'export_requested': True,
                'export_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'date_range': '最近5个月'
            }
            csv_file = save_statistics_to_csv(log_data, nickname)
            if csv_file:
                print(f"✅ 导出日志已保存到: {csv_file}")
        else:
            print("⚠️ 数据导出失败")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    # 添加测试选项
    print("请选择运行模式:")
    print("1. 正常运行主程序")
    print("2. 独立测试统计功能")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "2":
        test_statistics_only()
    else:
        main(None,None)
    # start_gui()


